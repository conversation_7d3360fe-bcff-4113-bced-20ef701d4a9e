"use client";

import React from "react";
import Link from "next/link";
import { motion } from "motion/react";
import JsConsultants from "../intro/JsConsultants";
import {
  Phone,
  Mail,
  MapPin,
  Clock,
  ArrowRight,
  Zap,
  Settings,
  Shield,
  Droplets,
  Building,
} from "lucide-react";
import {
  HeaderServices,
  NAVIGATION_ITEMS,
  CONTACT_INFO,
} from "@/utils/constants";
import Image from "next/image";

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const iconMap = {
    electrical: Zap,
    hvac: Settings,
    lighting: Droplets,
    "data-center": Building,
    plumbing: Droplets,
    "fire-safety": Shield,
    "solar-power": Zap,
    "energy-audits": Zap,
    ibms: Settings,
  };

  const services = HeaderServices.map((service) => ({
    name: service.title,
    href: service.href,
    icon: iconMap[service.id as keyof typeof iconMap] || Zap,
  }));

  const quickLinks = NAVIGATION_ITEMS;

  const contactInfo = [
    { icon: Phone, text: CONTACT_INFO.phone },
    { icon: Mail, text: CONTACT_INFO.email },
    { icon: MapPin, text: CONTACT_INFO.address },
    { icon: Clock, text: CONTACT_INFO.businessHours },
  ];

  return (
    <footer className="bg-gray-900 text-white">
      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              <div className="flex items-center space-x-3">
               <Image src="/logo-white-svg.svg" alt="JS Consultants" width={240} height={68} />
              </div>

              <p className="text-gray-300 leading-relaxed">
                A specialized electrical and MEP engineering firm committed to
                delivering the finest in design and execution for your most
                ambitious projects.
              </p>

              {/* <div className="space-y-3">
                <div className="flex items-center space-x-2 text-sm text-gray-400">
                  <Building className="w-4 h-4" />
                  <span>GST: 33XXXXX1234X1ZX</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-400">
                  <MapPin className="w-4 h-4" />
                  <span>Registered in Tamil Nadu, India</span>
                </div>
              </div> */}
            </motion.div>
          </div>

          {/* Quick Links */}
          <div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <h3 className="text-lg font-semibold mb-6">Quick Links</h3>
              <ul className="space-y-3">
                {quickLinks.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-gray-300 hover:text-white transition-colors duration-200 flex items-center group"
                    >
                      <ArrowRight className="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                      <span className="group-hover:translate-x-1 transition-transform duration-200">
                        {link.name}
                      </span>
                    </Link>
                  </li>
                ))}
              </ul>
            </motion.div>
          </div>

          {/* Services */}
          <div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <h3 className="text-lg font-semibold mb-6">Our Services</h3>
              <ul className="space-y-3">
                {services.map((service) => {
                  const IconComponent = service.icon;
                  return (
                    <li key={service.name}>
                      <Link
                        href={service.href}
                        className="text-gray-300 hover:text-white transition-colors duration-200 flex items-center group"
                      >
                        <IconComponent className="w-4 h-4 mr-3 text-blue-400" />
                        <span className="group-hover:translate-x-1 transition-transform duration-200">
                          {service.name}
                        </span>
                      </Link>
                    </li>
                  );
                })}
              </ul>
            </motion.div>
          </div>

          {/* Contact Info */}
          <div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <h3 className="text-lg font-semibold mb-6">Contact Info</h3>
              <ul className="space-y-4">
                {contactInfo.map((info, index) => {
                  const IconComponent = info.icon;
                  return (
                    <li key={index} className="flex items-start space-x-3">
                      <IconComponent className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-300 text-sm leading-relaxed">
                        {info.text}
                      </span>
                    </li>
                  );
                })}
              </ul>

              {/* CTA Button */}
              <motion.div
                className="mt-8"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Link
                  href="/enquire#enquiry-form"
                  className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Get Quote
                  <ArrowRight className="ml-2 w-4 h-4" />
                </Link>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0"
          >
            <div className="text-sm text-gray-400">
              © {currentYear} JS Consultants. All rights reserved.
            </div>

            <div className="flex items-center space-x-6 text-sm text-gray-400">
              <Link
                href="/privacy"
                className="hover:text-white transition-colors"
              >
                Privacy Policy
              </Link>
              <Link
                href="/terms"
                className="hover:text-white transition-colors"
              >
                Terms of Service
              </Link>
              <span>Made with ❤️ in India</span>
            </div>
          </motion.div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
