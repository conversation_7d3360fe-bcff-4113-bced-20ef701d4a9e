"use client";
import { Metadata } from "next";

const metadata: Metadata = {
  title:
    "Enquire Now | JS Consultants - Get Expert MEP Engineering Consultation",
  description:
    "Start your MEP engineering project with JS Consultants. Compare our services, explore FAQs, and get expert consultation for electrical, HVAC, plumbing, and fire safety systems. 8+ years of experience.",
  keywords:
    "MEP engineering consultation, electrical engineering services, HVAC design, plumbing engineering, fire safety systems, building automation, energy audit, JS Consultants enquiry",
  openGraph: {
    title: "Enquire Now | JS Consultants - Expert MEP Engineering Services",
    description:
      "Get professional MEP engineering consultation from industry experts with 8+ years of experience. Start your project today.",
    type: "website",
  },
};


import React from "react";
import { motion } from "motion/react";
import { useInView } from "react-intersection-observer";
import {
  CheckCircle,
  X,
  Star,
  Phone,
  MessageSquare,
} from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ConsultationForm } from "@/components/forms/ConsultationForm";

const EnquirePage = () => {
  const [heroRef, heroInView] = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });
  const [faqRef, faqInView] = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });
  const [comparisonRef, comparisonInView] = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });
  const [formRef, formInView] = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  // FAQ Data
  const faqs = [
    {
      question: "What MEP engineering services do you provide?",
      answer:
        "We provide comprehensive MEP engineering services including electrical systems design, HVAC systems, plumbing and fire protection, energy audits, building automation, lighting design, and data center infrastructure. Our team has 8+ years of experience in residential, commercial, and industrial projects.",
    },
    {
      question: "How long does a typical MEP project take?",
      answer:
        "Project timelines vary based on complexity and scope. Small residential projects typically take 2-4 weeks, commercial projects 4-12 weeks, and large industrial projects 3-6 months. We provide detailed timelines during our initial consultation and keep you updated throughout the process.",
    },
    {
      question: "Do you provide 3D modeling and BIM services?",
      answer:
        "Yes, we use advanced 3D modeling and Building Information Modeling (BIM) technologies for all our projects. This ensures accurate design coordination, clash detection, and seamless collaboration with architects and contractors.",
    },
    {
      question: "What is your project consultation process?",
      answer:
        "Our consultation process begins with understanding your project requirements, followed by site assessment, preliminary design review, detailed engineering, and ongoing support. We provide transparent communication and regular updates throughout the project lifecycle.",
    },
    {
      question: "Do you offer energy efficiency and sustainability consulting?",
      answer:
        "Absolutely! We specialize in energy-efficient design solutions, green building certifications, renewable energy integration, and sustainability consulting. Our energy audit services help optimize building performance and reduce operational costs.",
    },
    {
      question: "What are your payment terms and project costs?",
      answer:
        "Our pricing is competitive and transparent, based on project scope and complexity. We offer flexible payment terms with milestone-based billing. Contact us for a detailed quote tailored to your specific requirements.",
    },
  ];

  // Comparison Data
  const comparisonData = [
    {
      feature: "Years of Experience",
      jsConsultants: "8+ Years",
      competitor1: "10-15 Years",
    },
    {
      feature: "3D Modeling & BIM",
      jsConsultants: true,
      competitor1: true,
    },
    {
      feature: "Energy Audit Services",
      jsConsultants: true,
      competitor1: false,
    },
    {
      feature: "24/7 Support",
      jsConsultants: true,
      competitor1: false,
    },
    {
      feature: "Green Building Certification",
      jsConsultants: true,
      competitor1: true,
    },
    {
      feature: "Data Center Expertise",
      jsConsultants: true,
      competitor1: false,
    },
    {
      feature: "Solar Integration",
      jsConsultants: true,
      competitor1: true,
    },
    {
      feature: "Project Management",
      jsConsultants: true,
      competitor1: true,
    },
    {
      feature: "Cost Optimization",
      jsConsultants: true,
      competitor1: false,
    },
    {
      feature: "Compliance Expertise",
      jsConsultants: true,
      competitor1: true,
    },
  ];

  return (
    <div className="min-h-screen  bg-blue-50/20">
      {/* Hero Section */}
      <section ref={heroRef} className="pt-16 md:pt-20 lg:pt-24 pb-12 md:pb-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={heroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-slate-800 mb-4 md:mb-6">
              Ready to Start Your
              <span className="text-blue-600 block">MEP Project?</span>
            </h1>
            <p className="text-base md:text-lg lg:text-xl text-slate-600 mb-6 md:mb-8 max-w-3xl mx-auto">
              Get expert consultation from our experienced MEP engineering team.
              Compare our services, explore our expertise, and start your
              project with confidence.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 md:gap-4 justify-center">
              <motion.a
                href="#enquiry-form"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center justify-center px-6 md:px-8 py-3 md:py-4 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors text-sm md:text-base"
              >
                <MessageSquare className="mr-2 w-4 h-4 md:w-5 md:h-5" />
                Start Your Enquiry
              </motion.a>
              <motion.a
                href="tel:+919876543210"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center justify-center px-6 md:px-8 py-3 md:py-4 bg-white text-blue-600 font-semibold rounded-lg border-2 border-blue-600 hover:bg-blue-50 transition-colors text-sm md:text-base"
              >
                <Phone className="mr-2 w-4 h-4 md:w-5 md:h-5" />
                Call Now
              </motion.a>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Company Comparison Section */}
      {/* Company Comparison Section */}
      <section
        ref={comparisonRef}
        className="py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8"
      >
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={comparisonInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center mb-8 sm:mb-12 lg:mb-16"
          >
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-slate-800 mb-3 sm:mb-4 lg:mb-6 px-2">
              Why Choose JS Consultants?
            </h2>
            <p className="text-base sm:text-lg md:text-xl text-slate-600 max-w-3xl mx-auto px-4">
              See how we compare to other MEP engineering firms in the industry
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={comparisonInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="flex justify-center"
          >
            {/* Mobile View (Cards) */}
            <div className="block md:hidden w-full max-w-md mx-auto space-y-4">
              {comparisonData.map((row, index) => (
                <div
                  key={index}
                  className="bg-white rounded-xl shadow-lg p-6 border border-slate-200"
                >
                  <h3 className="font-semibold text-slate-800 mb-4 text-lg">
                    {row.feature}
                  </h3>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center">
                        <Star className="w-4 h-4 mr-2 text-blue-600" />
                        <span className="font-medium text-blue-800 text-sm">
                          JS Consultants
                        </span>
                      </div>
                      <div className="flex items-center">
                        {typeof row.jsConsultants === "boolean" ? (
                          row.jsConsultants ? (
                            <CheckCircle className="w-5 h-5 text-green-600" />
                          ) : (
                            <X className="w-5 h-5 text-red-500" />
                          )
                        ) : (
                          <span className="font-semibold text-blue-600 text-sm">
                            {row.jsConsultants}
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                      <span className="font-medium text-slate-700 text-sm">
                        Other MEP Consultants
                      </span>
                      <div className="flex items-center">
                        {typeof row.competitor1 === "boolean" ? (
                          row.competitor1 ? (
                            <CheckCircle className="w-5 h-5 text-green-600" />
                          ) : (
                            <X className="w-5 h-5 text-red-500" />
                          )
                        ) : (
                          <span className="text-slate-600 text-sm">
                            {row.competitor1}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Desktop/Tablet View (Table) */}
            <div className="hidden md:block w-full">
              <div className="overflow-x-auto">
                <div className="min-w-full bg-white rounded-2xl shadow-xl overflow-hidden border border-slate-200">
                  <Table className="min-w-full">
                    <TableHeader>
                      <TableRow className="bg-gradient-to-r from-slate-50 to-slate-100 border-b border-slate-200">
                        <TableHead className="font-bold text-slate-800 py-5 px-6 md:px-8 lg:px-12 text-left text-sm md:text-base">
                          Features
                        </TableHead>
                        <TableHead className="font-bold text-blue-600 py-5 px-4 md:px-6 lg:px-8 text-center text-sm md:text-base min-w-[180px]">
                          <div className="flex items-center justify-center">
                            <Star className="w-4 h-4 md:w-5 md:h-5 mr-2 flex-shrink-0" />
                            <span className="whitespace-nowrap">
                              JS Consultants
                            </span>
                          </div>
                        </TableHead>
                        <TableHead className="font-bold text-slate-700 py-5 px-4 md:px-6 lg:px-8 text-center text-sm md:text-base min-w-[180px]">
                          <span className="whitespace-nowrap">
                            Other MEP Consultants
                          </span>
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody className="divide-y divide-slate-100">
                      {comparisonData.map((row, index) => (
                        <TableRow
                          key={index}
                          className="hover:bg-slate-50 transition-colors duration-200 group"
                        >
                          <TableCell className="font-medium py-5 px-6 md:px-8 lg:px-12 text-slate-800 text-sm md:text-base group-hover:text-slate-900">
                            {row.feature}
                          </TableCell>
                          <TableCell className="text-center py-5 px-4 md:px-6 lg:px-8">
                            <div className="flex justify-center">
                              {typeof row.jsConsultants === "boolean" ? (
                                row.jsConsultants ? (
                                  <CheckCircle className="w-5 h-5 md:w-6 md:h-6 text-green-600 drop-shadow-sm" />
                                ) : (
                                  <X className="w-5 h-5 md:w-6 md:h-6 text-red-500 drop-shadow-sm" />
                                )
                              ) : (
                                <span className="font-semibold text-blue-600 text-sm md:text-base px-2 py-1 bg-blue-50 rounded-lg">
                                  {row.jsConsultants}
                                </span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-center py-5 px-4 md:px-6 lg:px-8">
                            <div className="flex justify-center">
                              {typeof row.competitor1 === "boolean" ? (
                                row.competitor1 ? (
                                  <CheckCircle className="w-5 h-5 md:w-6 md:h-6 text-green-600 drop-shadow-sm" />
                                ) : (
                                  <X className="w-5 h-5 md:w-6 md:h-6 text-red-500 drop-shadow-sm" />
                                )
                              ) : (
                                <span className="text-slate-600 text-sm md:text-base px-2 py-1 bg-slate-50 rounded-lg">
                                  {row.competitor1}
                                </span>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Enquiry Form Section */}
      <section
        id="enquiry-form"
        ref={formRef}
        className="py-12 md:py-16 px-4 sm:px-6 lg:px-8"
      >
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={formInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center mb-8 md:mb-12"
          >
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-slate-800 mb-3 md:mb-4">
              Start Your Project Today
            </h2>
            <p className="text-base md:text-lg lg:text-xl text-slate-600">
              Fill out the form below and our experts will contact you within 24
              hours
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={formInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <ConsultationForm />
          </motion.div>
        </div>
      </section>

      {/* FAQ Section */}
      <section ref={faqRef} className="py-12 md:py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={faqInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center mb-8 md:mb-12"
          >
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-slate-800 mb-3 md:mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-base md:text-lg lg:text-xl text-slate-600">
              Get answers to common questions about our MEP engineering services
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={faqInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <Accordion type="single" collapsible className="space-y-3 md:space-y-4">
              {faqs.map((faq, index) => (
                <AccordionItem
                  key={index}
                  value={`item-${index}`}
                  className="bg-slate-50 rounded-lg px-4 md:px-6 border-none"
                >
                  <AccordionTrigger className="text-left font-semibold text-slate-800 hover:no-underline py-4 md:py-6 text-sm md:text-base">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-slate-600 pb-4 md:pb-6 leading-relaxed text-sm md:text-base">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default EnquirePage;
