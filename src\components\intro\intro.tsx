"use client";
import React from "react";
import {
  motion,
  useAnimation,
  useMotionValue,
  useTransform,
} from "framer-motion";
import JsConsultants from "./JsConsultants";
import SplitText from "./SplitText";

type Props = {
  onComplete?: () => void;
};

const Intro = ({ onComplete }: Props) => {
  const [count, setCount] = React.useState<number>(0);
  const [showText, setShowText] = React.useState(false);
  const [showLoader, setShowLoader] = React.useState(false);
  const [hideComponent, setHideComponent] = React.useState(false);
  const [animationStarted, setAnimationStarted] = React.useState(false);
  const [shouldShowIntro, setShouldShowIntro] = React.useState(true);
  const [isFirstVisit, setIsFirstVisit] = React.useState(true);

  const logoControls = useAnimation();
  const gridControls = useAnimation();
  const loaderProgress = useMotionValue(0);
  const contentControls = useAnimation();

  const loaderWidth = useTransform(loaderProgress, [0, 100], ["0%", "100%"]);

  // Check session storage on mount
  React.useEffect(() => {
    const hasVisited = sessionStorage.getItem("hasVisitedHome");
    if (hasVisited) {
      setShouldShowIntro(false);
      setIsFirstVisit(false);
      setHideComponent(true);
      // Call onComplete immediately if not first visit
      if (onComplete) {
        onComplete();
      }
    } else {
      // Mark as visited for this session
      sessionStorage.setItem("hasVisitedHome", "true");
    }
  }, [onComplete]);

  React.useEffect(() => {
    // Only run animation if it hasn't started yet and should show intro
    if (animationStarted || !shouldShowIntro) return;
    
    setAnimationStarted(true);

    const animateSequence = async () => {
      // Small delay to ensure component is mounted
      await new Promise((resolve) => setTimeout(resolve, 100));

      // First animation: Logo translate from center to left AND fade in
      await logoControls.start({
        x: 0,
        opacity: 1,
        transition: {
          duration: 1,
          ease: [0.25, 0.46, 0.45, 0.94],
        },
      });

      // Second animation: Show split text
      setShowText(true);

      // Wait for text animation to complete
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Third animation: Show and animate loading bar
      setShowLoader(true);

      // Animate loading progress
      const loadingAnimation = setInterval(() => {
        setCount((prev) => {
          const newValue = Math.min(prev + Math.random() * 4 + 1, 100);
          loaderProgress.set(newValue);

          if (newValue >= 100) {
            clearInterval(loadingAnimation);
            // Trigger grid animation when count reaches 100
            setTimeout(() => {
              contentControls.start({
                opacity: 0,
                transition: { duration: 0.4, ease: "easeOut" },
              });
              setTimeout(() => {
                gridControls.start({ scaleY: 0 });
              }, 400);
            }, 500);
            return 100;
          }
          return newValue;
        });
      }, 80);
    };

    animateSequence();
  }, [animationStarted, shouldShowIntro, logoControls, gridControls, contentControls, loaderProgress]);

  // Handle grid animation completion
  const handleGridAnimationComplete = () => {
    setHideComponent(true);
    setShouldShowIntro(false);
    // Call the onComplete callback when animation finishes
    if (onComplete) {
      onComplete();
    }
  };

  // Don't render the component if it should be hidden or not shown
  if (hideComponent || !shouldShowIntro) {
    return null;
  }

  return (
    <div className="h-screen absolute w-full z-50">
      <div className="flex flex-col items-center justify-center overflow-hidden relative h-screen">
        {/* Background grid with conditional animation */}
        <motion.div
          className="grid grid-cols-10 w-full absolute top-0 left-0 z-10"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          {Array(10)
            .fill(0)
            .map((_, i) => (
              <motion.div
                key={`grid-${i}`}
                className="h-screen bg-gray-50"
                initial={{ scaleY: 1 }}
                animate={gridControls}
                transition={{
                  duration: 0.6,
                  delay: i * 0.1,
                  ease: "easeInOut",
                }}
                style={{ originY: 1 }}
                onAnimationComplete={
                  i === 9 ? handleGridAnimationComplete : undefined
                }
              />
            ))}
        </motion.div>

        {/* Main content */}
        <motion.div
          className="z-20 relative"
          animate={contentControls}
          initial={{ opacity: 1 }}
        >
          <div className="flex items-center gap-2">
            {/* JS Logo with Framer Motion animation */}
            <motion.div
              animate={logoControls}
              initial={{ x: 50, opacity: 0 }}
              transition={{ duration: 1 }}
            >
              <motion.div
                whileHover={{
                  scale: 1.1,
                  rotate: 5,
                  transition: { duration: 0.2 },
                }}
                whileTap={{ scale: 0.95 }}
              >
                <JsConsultants className="w-10 h-10" />
              </motion.div>
            </motion.div>

            {/* Split text with stagger animation */}
            <motion.div
              key="text-animation"
              initial={{ opacity: 0, x: 50 }}
              animate={
                showText
                  ? {
                      opacity: 1,
                      x: 0,
                      transition: {
                        duration: 0.6,
                        ease: "easeOut",
                      },
                    }
                  : {}
              }
            >
              {showText && (
                <SplitText
                  text="Consultants"
                  splitType="chars"
                  delay={50}
                  duration={0.5}
                  className="text-2xl font-bold font-space-mono"
                />
              )}
            </motion.div>
          </div>

          {/* Loading bar with Framer Motion */}
          <motion.div
            key="loader-animation"
            initial={{ opacity: 0 }}
            animate={
              showLoader
                ? {
                    opacity: 1,
                    transition: {
                      duration: 0.8,
                      ease: [0.25, 0.46, 0.45, 0.94],
                    },
                  }
                : {}
            }
            className="mt-8 flex justify-center item-center"
          >
            {/* Loading bar container */}
            <motion.div
              className="w-48 bg-gray-100 rounded-full h-2 overflow-hidden shadow-inner"
              initial={{ scaleX: 0 }}
              animate={showLoader ? { scaleX: 1 } : {}}
              transition={{ duration: 0.5, ease: "easeOut" }}
              style={{ originX: 0 }}
            >
              <motion.div
                className="bg-gray-300 h-full rounded-full relative overflow-hidden"
                style={{ width: loaderWidth }}
              >
                {/* Shimmer effect */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-white/50 to-transparent"
                  animate={{
                    x: ["-100%", "100%"],
                  }}
                  transition={{
                    repeat: Infinity,
                    duration: 1.5,
                    ease: "linear",
                  }}
                  style={{ width: "50%" }}
                />
              </motion.div>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default Intro;