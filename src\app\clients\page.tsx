"use client";
import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import Link from "next/link";
import { clientLogos, Logo } from "@/utils/constants";
import { Filter, ExternalLink, ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";

const projectFilter = [
  { label: "All", filter: "all" },
  { label: "Textiles", filter: "textiles" },
  { label: "Residentials", filter: "residentials" },
  { label: "IT Sector & Offices", filter: "it-sector-offices" },
  { label: "Commercial", filter: "commercial" },
  { label: "Education", filter: "education" },
  { label: "Industrial", filter: "industrial" },
  { label: "Healthcare", filter: "healthcare" },
];

const PROJECTS_PER_PAGE = 17;

const Page = () => {
  const [heroRef, heroInView] = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });
  const [clientsRef, clientsInView] = useInView({
    threshold: 0.2,
    triggerOnce: true,
  });

  const [filteredClients, setFilteredClients] = useState(clientLogos);
  const [selectedProjectFilter, setSelectedProjectFilter] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);

  const totalPages = Math.ceil(filteredClients.length / PROJECTS_PER_PAGE);
  const startIndex = (currentPage - 1) * PROJECTS_PER_PAGE;
  const endIndex = startIndex + PROJECTS_PER_PAGE;
  const currentClients = filteredClients.slice(startIndex, endIndex);

  useEffect(() => {
    if (selectedProjectFilter === "all") {
      setFilteredClients(clientLogos);
    } else {
      const filtered = clientLogos.filter(
        (project: Logo) =>
          (project.type || "").toLowerCase() ===
          selectedProjectFilter.toLowerCase()
      );
      setFilteredClients(filtered);
    }
    setCurrentPage(1);
  }, [selectedProjectFilter]);

  const handleFilterChange = (filter: string) => {
    setSelectedProjectFilter(filter);
  };

  const handlePageChange = (page: number) => {
    if (page === currentPage) return;
    setIsLoading(true);
    setTimeout(() => {
      setCurrentPage(page);
      setIsLoading(false);
      (
        clientsRef as unknown as { current: HTMLElement | null }
      )?.current?.scrollIntoView({ behavior: "smooth" });
    }, 100);
  };

  const getPaginationButtons = () => {
    const buttons = [];
    const maxVisibleButtons = 5;
    let startPage = Math.max(
      1,
      currentPage - Math.floor(maxVisibleButtons / 2)
    );
    const endPage = Math.min(totalPages, startPage + maxVisibleButtons - 1);
    if (endPage - startPage + 1 < maxVisibleButtons) {
      startPage = Math.max(1, endPage - maxVisibleButtons + 1);
    }
    for (let i = startPage; i <= endPage; i++) {
      buttons.push(i);
    }
    return buttons;
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section
        ref={heroRef}
        className="relative py-12 md:py-16 lg:py-20 overflow-hidden"
      >
        <div className="absolute inset-0">
          <Image
            src="/images/clients/project-1.jpg"
            alt="Clients"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-slate-900/90 via-slate-900/70 to-slate-900/50"></div>
        </div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={heroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center space-y-4 md:space-y-6"
          >
            <h1 className="text-4xl lg:text-6xl font-bold text-white">
              Our <span className="text-blue-400">Engineering Clients</span>
            </h1>
            <p className="text-lg text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Explore our portfolio of successful MEP engineering clients across
              diverse industries.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Filter */}
      <section className="py-6 md:py-8 bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap items-center justify-center gap-2 md:gap-4">
            <div className="flex items-center space-x-2 text-gray-600 mb-2 md:mb-0">
              <Filter className="w-5 h-5" />
              <span className="font-medium text-base">Project Type:</span>
            </div>
            {projectFilter.map((filter) => (
              <motion.button
                key={filter.label}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => handleFilterChange(filter.filter)}
                className={`px-4 py-2 rounded-full font-medium transition-colors text-base ${
                  selectedProjectFilter === filter.filter
                    ? "bg-blue-600 text-white"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                }`}
              >
                {filter.label}
              </motion.button>
            ))}
          </div>
        </div>
      </section>

      {/* Clients Grid */}
      <section
        ref={clientsRef}
        className="py-12 md:py-16 lg:py-20 bg-blue-50/50"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {isLoading ? (
            <div className="flex justify-center h-screen md:h-[146dvh] items-center min-h-[200px]">
              <div className="animate-spin rounded-full h-10 w-10 border-4 border-blue-600 border-t-transparent"></div>
            </div>
          ) : filteredClients.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 text-lg">
                No clients found for the selected filter.
              </p>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 auto-rows-[1fr]">
                {currentClients.map((project, index) => (
                  <div
                    key={project.name}
                    className={`bg-white p-4 rounded-xl shadow-md flex items-center flex-col justify-center relative ${
                      index % 2 === 0 ? "row-span-2" : "row-span-1"
                    }`}
                  >
                    <Image
                      src={project.src}
                      alt={project.name}
                      width={200}
                      height={120}
                      className="w-auto h-auto object-contain max-h-28"
                    />
                    <p className="text-center text-gray-700 text-sm md:text-base mt-2 font-mono">
                      {project.name}
                    </p>
                  </div>
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-center mt-8 md:mt-12 space-x-2">
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={`flex items-center px-3 py-2 rounded-lg font-medium text-base ${
                      currentPage === 1
                        ? "text-gray-400 cursor-not-allowed"
                        : "text-gray-700 hover:bg-gray-100"
                    }`}
                  >
                    <ChevronLeft className="w-4 h-4 mr-1" />
                    Prev
                  </button>
                  {getPaginationButtons().map((page) => (
                    <button
                      key={page}
                      onClick={() => handlePageChange(page)}
                      className={`px-4 py-2 rounded-lg font-medium text-base ${
                        currentPage === page
                          ? "bg-blue-600 text-white"
                          : "text-gray-700 hover:bg-gray-100"
                      }`}
                    >
                      {page}
                    </button>
                  ))}
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className={`flex items-center px-3 py-2 rounded-lg font-medium text-base ${
                      currentPage === totalPages
                        ? "text-gray-400 cursor-not-allowed"
                        : "text-gray-700 hover:bg-gray-100"
                    }`}
                  >
                    Next
                    <ChevronRight className="w-4 h-4 ml-1" />
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </section>

      {/* CTA */}
      <section className="px-4 sm:px-6 md:px-8 lg:px-10 py-4 md:py-5">
        <div className="max-w-7xl bg-slate-800 mx-auto px-4 py-8 rounded-xl text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={clientsInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="space-y-4"
          >
            <h2 className="text-3xl font-bold text-white">
              Have a Project in Mind?
            </h2>
            <p className="text-lg text-blue-100 max-w-2xl mx-auto">
              Let&apos;s discuss how we can bring your vision to life with our
              expertise.
            </p>
            <Link href="/enquire#enquiry-form">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center px-6 py-3 text-white bg-blue-600 font-semibold rounded-lg hover:bg-blue-700 transition-colors"
              >
                Enquire Now
                <ExternalLink className="ml-2 w-5 h-5" />
              </motion.button>
            </Link>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Page;
