"use client";

import React from "react";
import { motion } from "motion/react";
import { <PERSON>R<PERSON>, Zap, Building } from "lucide-react";

import LineSVG from "@/assets/svg/LineSVG";
import Link from "next/link";

const Hero = () => {
  return (
    <div className="relative w-full  bg-blue-50/50 overflow-hidden border-b border-gray-200">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundRepeat: "repeat",
          }}
        ></div>
      </div>

      <div className="relative z-10 max-w-7xl px-4 sm:pl-6 lg:pl-8 mx-auto">
        <div className="pt-2 md:pt-4 lg:pt-6 xl:pt-12 ">
          {/* Main Hero Content */}
          <div className="grid lg:grid-cols-2 gap-8 md:gap-12 items-center">
            {/* Left Column - Text Content */}
            <motion.div
              initial={{ opacity: 0.5, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: 0.05 }}
              className="space-y-3 sm:space-x-6 mt-5"
            >
              {/* Badge */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
                className="inline-flex items-center px-3 md:px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-xs md:text-sm font-medium"
              >
                <Zap className="w-3 h-3 md:w-4 md:h-4 mr-2" />
                <span className="hidden sm:inline">
                  Electrical & MEP Engineering Excellence
                </span>
                <span className="sm:hidden text-xs">MEP Engineering Excellence</span>
              </motion.div>

              {/* Main Headline */}
              <div className="space-y-3 md:space-y-4">
                <motion.h1
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.2 }}
                  className="text-3xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-gray-900 leading-tight "
                >
                  <span className="block sm:inline">Transforming Your</span>{" "}
                  <span className="text-blue-600 relative block sm:inline">
                    Dream Projects
                    <LineSVG
                      className="absolute bottom-1 left-0 right-0 -z-10 origin-left hidden sm:block"
                      width="100%"
                    />
                  </span>{" "}
                  <span className="block sm:inline">Into Reality</span>
                </motion.h1>

                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.3 }}
                  className="text-base md:text-lg lg:text-xl text-gray-600 leading-relaxed max-w-2xl"
                >
                  JS Consultants is a specialized electrical and MEP engineering
                  firm committed to delivering the finest in design and
                  execution for your most ambitious projects.
                </motion.p>
              </div>

              {/* CTA Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.4 }}
                className="flex flex-row sm:flex-row gap-3 md:gap-4"
              >
                <Link href="/enquire#enquiry-form">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="inline-flex items-center justify-center px-6 md:px-8 py-3 md:py-4 bg-blue-600 text-white font-semibold rounded-lg shadow-lg hover:bg-blue-700 transition-colors text-sm md:text-base"
                  >
                    Enquire Now
                    <ArrowRight className="ml-2 w-4 h-4 md:w-5 md:h-5" />
                  </motion.button>
                </Link>
                <Link href="/projects">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="inline-flex items-center justify-center px-6 md:px-8 py-3 md:py-4 border-2 border-gray-300 text-gray-700 font-semibold rounded-lg hover:border-blue-600 hover:text-blue-600 transition-colors text-sm md:text-base"
                  >
                    View Our Work
                  </motion.button>
                </Link>
              </motion.div>

              {/* Stats */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.5 }}
                className="grid grid-cols-3 gap-4 md:gap-8 pt-2 md:pt-4 pb-2 border-t border-gray-200"
              >
                <div className="text-center">
                  <div className="text-lg md:text-2xl font-bold text-gray-900">
                    10+
                  </div>
                  <div className="text-xs md:text-sm text-gray-600">
                    Years Experience
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-lg md:text-2xl font-bold text-gray-900">
                    150+
                  </div>
                  <div className="text-xs md:text-sm text-gray-600">
                    Projects Completed
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-lg md:text-2xl font-bold text-gray-900">
                    8
                  </div>
                  <div className="text-xs md:text-sm text-gray-600">
                    Core Services
                  </div>
                </div>
              </motion.div>
            </motion.div>

            {/* Right Column - Service Highlights */}
            <motion.div
              initial={{ opacity: 0, y: 100 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.2 }}
              className="w-full justify-center items-center flex md:w-80 lg:w-96 xl:w-[600px] h-64 md:h-80 lg:h-96 xl:h-[600px] order-last lg:order-last overflow-clip"
            >
              <video
                src={"/output6.webm"}
                autoPlay
                loop
                muted
                playsInline
                className="w-full h-full object-cover "
              />
            </motion.div>
          </div>
        </div>
      </div>

      {/* Floating Elements - Hidden on mobile */}
      <motion.div
        animate={{
          y: [0, -20, 0],
          rotate: [0, 5, 0],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut",
        }}
        className="absolute z-50 top-1/4 right-4 md:right-10 w-12 h-12 md:w-16 md:h-16 bg-blue-100 rounded-full items-center justify-center opacity-60 hidden md:flex"
      >
        <Zap className="w-6 h-6 md:w-8 md:h-8 text-blue-600" />
      </motion.div>

      <motion.div
        animate={{
          y: [0, 15, 0],
          rotate: [0, -3, 0],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1,
        }}
        className="absolute bottom-1/4 left-10 w-12 h-12 bg-green-100 rounded-full flex items-center justify-center opacity-60"
      >
        <Building className="w-6 h-6 text-green-600" />
      </motion.div>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 1, repeat: Infinity }}
          className="w-6 h-10 border-2 border-blue-700/30 rounded-full flex justify-center"
        >
          <motion.div
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 1, repeat: Infinity }}
            className="w-1 h-3 bg-blue-700/60 rounded-full mt-2"
          />
        </motion.div>
      </motion.div>
    </div>
  );
};

export default Hero;
