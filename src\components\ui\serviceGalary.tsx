"use client";

import { galaryImages } from "@/utils/constants";
import React from "react";
import { motion } from "framer-motion";

type Props = {};

const ServiceGalary = (props: Props) => {
  // Duplicate images to simulate infinite scroll
  const row1Images = [...galaryImages, ...galaryImages];
  const row2Images = [
    ...galaryImages.slice().reverse(),
    ...galaryImages.slice().reverse(),
  ]; // reverse for variation

  return (
    <div className="overflow-hidden bg-inherit ">
      <h1 className="text-3xl font-space-mono font-semibold text-start mb-10">
        Gallery
      </h1>
      <div className="relative">
        <div className="absolute top-0 left-0 h-full w-12 md:w-24 bg-gradient-to-r from-white via-white/70 to-white/0 pointer-events-none z-10" />
        <div className="absolute top-0 right-0 h-full w-12 md:w-24 bg-gradient-to-l from-white via-white/70 to-white/0 pointer-events-none z-10" />
        {/* Row 1 */}
        <div className="overflow-hidden mb-6">
          <motion.div
            className="flex gap-4 w-max"
            animate={{ x: ["0%", "-10%"] }}
            transition={{
              duration: 60,
              repeat: Infinity,
              ease: "linear",
            }}
          >
            {row1Images.map((image, i) => (
              <div
                key={`row1-${i}`}
                className="min-w-[300px] h-[200px] rounded-lg overflow-hidden"
              >
                <img
                  src={image.src}
                  alt={image.alt}
                  className="w-full h-full object-cover"
                />
              </div>
            ))}
          </motion.div>
        </div>

        {/* Row 2 */}
        <div className="overflow-hidden">
          <motion.div
            className="flex gap-4 w-max"
            animate={{ x: ["0%", "-10%"] }}
            transition={{
              duration: 70, // slightly slower for staggered effect
              repeat: Infinity,
              ease: "linear",
            }}
          >
            {row2Images.map((image, i) => (
              <div
                key={`row2-${i}`}
                className="min-w-[300px] h-[200px] rounded-lg overflow-hidden"
              >
                <img
                  src={image.src}
                  alt={image.alt}
                  className="w-full h-full object-cover"
                />
              </div>
            ))}
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default ServiceGalary;
