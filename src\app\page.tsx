"use client";
import Hero from "@/components/hero/Hero";
import ServicesOverview from "@/components/home/<USER>";
import FeaturedProjects from "@/components/home/<USER>";
import CompanyHighlights from "@/components/home/<USER>";
import TestimonialsSection from "@/components/home/<USER>";
import ClientsSection from "@/components/home/<USER>";
import CallToActionSection from "@/components/home/<USER>";
import { clientLogos } from "@/utils/constants";

export default function Home() {


  return (
    <main className="relative">
      <div className={`relative z-0`}>
        <Hero />
        <ServicesOverview />
        <FeaturedProjects />
        <CompanyHighlights />
        <TestimonialsSection />
        <ClientsSection className="py-16" clients={clientLogos} />
        <CallToActionSection />
      </div>
    </main>
  );
}
