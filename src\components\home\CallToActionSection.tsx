import React from "react";

import Link from "next/link";
import { <PERSON><PERSON> } from "../ui/button";

const CallToActionSection = () => {
  return (
    <div className="px-4 sm:px-6 md:px-8 lg:px-10 py-4 md:py-5">
      <div className="text-center mt-2 md:mt-4 lg:mt-6">
        <div className="bg-slate-900/90 rounded-xl md:rounded-2xl p-6 md:p-8 lg:p-12">
          <h3 className="text-xl md:text-2xl lg:text-3xl font-bold text-white mb-3 md:mb-4">
            Experience the JS Consultants Difference
          </h3>
          <p className="text-gray-50 mb-6 md:mb-8 max-w-5xl mx-auto text-sm md:text-base">
            Partner with a team that brings architectural precision, technical
            excellence, and innovative thinking to every MEP design. We’re
            committed to delivering efficient, future-ready solutions tailored
            to your project&apoes;s vision and scale.
          </p>
          <div className="flex flex-row  gap-3 md:gap-4 justify-center">
            <Link href="/enquire#enquiry-form">
              <button className="bg-blue-600 text-white px-6 md:px-8 py-3 md:py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors text-sm md:text-base">
                Enquire Now
              </button>
            </Link>
            <Link href={"/about"}>
              <button className="bg-white text-blue-600 px-6 md:px-8 py-3 md:py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors text-sm md:text-base">
                Learn About Us
              </button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CallToActionSection;
